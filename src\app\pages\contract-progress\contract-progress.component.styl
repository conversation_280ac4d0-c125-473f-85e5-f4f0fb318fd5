@import './../../../styles/definitions.styl'

.filter-title
  border-bottom 1px solid #dadce0
  height 45px !important
  line-height 40px !important
  padding-left 20px
  padding-right 20px
  font-size 15px !important
  font-weight bold !important
  color #626262

.widget mat-chip, .cdk-drag-preview
  background-color #e4e4e4 !important
  color #626262 !important

.mat-icon-sorting
  transform rotate(90deg) !important
  height 15px !important

.list-header
  position relative
  padding-top 2px
  padding-bottom 2px
  > div
    margin 0 6px
  .mat-checkbox
    margin-right 14px
    margin-left 14px
  .list-icons
    margin 0 6px
    .list-mini-label
      font-size 10px
      margin-left 8px
      margin-right 8px
    .mat-slide-toggle
      padding 0 10px
      border-left 1px solid colorLightGrayAlpha
      .mat-icon
        margin 8px 0

.list-item
  transition all 0.2s ease-in-out

.list-item:hover
  box-shadow inset 1px 0 0 #dadce0,
  inset -1px 0 0 #dadce0,
  0 0px 5px 0 rgba(60,64,67,0.3),
  1px 0px 0px 0px rgba(60,64,67,0.15)

.filter
  position relative
  width 100%

.unread-row
  background-color color16
  .list-sub-title, .meta-text
    font-weight normal

label
  padding-right 8px !important
  padding-bottom 5px !important

.sorting-list
  width 260px
  padding-right 15px !important
  padding-left 15px !important
  margin-left 4px !important
  margin-right  4px !important
  max-width 100%
  border  solid 1px #ccc
  display block
  background white
  border-radius 5px
  overflow hidden


.sorting-box
  padding 8px 5px
  border-bottom solid 1px #ccc
  color #626262
  display flex
  flex-direction row
  align-items center
  justify-content space-between
  box-sizing border-box
  cursor move
  background white
  font-size 11px

.cdk-drag-preview
  box-sizing border-box
  border-radius 5px
  background-color #e4e4e4 !important
  color #626262 !important
  box-shadow 0 5px 5px -3px rgba(0, 0, 0, 0.2),
          0 8px 10px 1px rgba(0, 0, 0, 0.14),
          0 3px 14px 2px rgba(0, 0, 0, 0.12)


.cdk-drag-placeholder
  opacity 0


.cdk-drag-animating
  transition transform 250ms cubic-bezier(0, 0, 0.2, 1)


.sorting-box:last-child
  border none


.sorting-list.cdk-drop-list-dragging .sorting-box:not(.cdk-drag-placeholder)
  transition transform 250ms cubic-bezier(0, 0, 0.2, 1)

.mat-flat-button
  font-size 12px !important

.font-weight-normal
  font-weight normal

.link-alternate-content
  color colorContentText
  font-weight bold
  font-size 11px

.main-title
  display inline-block
.ageops-link
  padding-right: 0px
  padding-left: 0px

::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline-start {
  width 1px !important;
}

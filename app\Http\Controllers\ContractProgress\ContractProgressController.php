<?php namespace AGEOPS\AETS\Http\Controllers\ContractProgress;

use AGEOPS\AETS\Helpers\DropDowns;
use AGEOPS\AETS\Helpers\Error;
use AGEOPS\AETS\Helpers\Http;
use AGEOPS\AETS\Http\Controllers\Controller;
use App\Helpers\HttpRequest;
use Illuminate\Http\Request;

class ContractProgressController extends Controller
{


    public function getContractList(Request $request){
        try {
            $q = $request->all();
            $queryParameters = [];
            \Log::info('the q is', $q);

            // Process page parameters
            if (isset($q['page_size'])) {
                $queryParameters['page_size'] = $q['page_size'];
            }
            if (isset($q['page_index'])) {
                $queryParameters['page_index'] = $q['page_index'];
            }

            // Process search criteria
            if (isset($q['bidding_search']) && !empty($q['bidding_search'])) {
                $queryParameters['bidding_search'] = $q['bidding_search'];
            }

            if (isset($q['general_search']) && !empty($q['general_search'])) {
                $queryParameters['general_search'] = $q['general_search'];
            }

            if (isset($q['procurement_processing_body_slug']) && !empty($q['procurement_processing_body_slug'])) {
                if (is_array($q['procurement_processing_body_slug'])) {
                    $queryParameters['procurement_processing_body_slug'] = array_column($q['procurement_processing_body_slug'], 'slug');
                } else {
                    $queryParameters['procurement_processing_body_slug'] = $q['procurement_processing_body_slug'];
                }
            }

            if (isset($q['procurement_method']) && !empty($q['procurement_method'])) {
                if (is_array($q['procurement_method'])) {
                    $queryParameters['procurement_method'] = $q['procurement_method'];
                } else {
                    $queryParameters['procurement_method'] = explode(',', $q['procurement_method']);
                }
            }

            if (isset($q['procurement_type_slug']) && !empty($q['procurement_type_slug'])) {
                if (is_array($q['procurement_type_slug'])) {
                    $queryParameters['procurement_type_slug'] = $q['procurement_type_slug'];
                } else {
                    $queryParameters['procurement_type_slug'] = explode(',', $q['procurement_type_slug']);
                }
            }

            if (isset($q['flow_status_slug']) && !empty($q['flow_status_slug'])) {
                if (is_array($q['flow_status_slug'])) {
                    $queryParameters['flow_status_slug'] = $q['flow_status_slug'];
                } else {
                    $queryParameters['flow_status_slug'] = explode(',', $q['flow_status_slug']);
                }
            }

            if (isset($q['procurement_entity_slug']) && !empty($q['procurement_entity_slug'])) {
                if (is_array($q['procurement_entity_slug'])) {
                    $queryParameters['procurement_entity_slug'] = array_column($q['procurement_entity_slug'], 'slug');
                } else {
                    $queryParameters['procurement_entity_slug'] = explode(',', $q['procurement_entity_slug']);
                }
            }

            if (isset($q['province_slug']) && !empty($q['province_slug'])) {
                if (is_array($q['province_slug'])) {
                    $queryParameters['province_slug'] = array_column($q['province_slug'], 'slug');
                } else {
                    $queryParameters['province_slug'] = explode(',', $q['province_slug']);
                }
            }

            if (isset($q['fiscal_year']) && !empty($q['fiscal_year'])) {
                if (is_array($q['fiscal_year'])) {
                    $queryParameters['fiscal_year'] = $q['fiscal_year'];
                } else {
                    $queryParameters['fiscal_year'] = explode(',', $q['fiscal_year']);
                }
            }

            if (isset($q['contract_status']) && !empty($q['contract_status'])) {
                $queryParameters['contract_status'] = $q['contract_status'];
            }

            // Process sorting criteria
            if (isset($q['sort_criteria']) && !empty($q['sort_criteria'])) {
                $queryParameters['sort_criteria'] = $q['sort_criteria'];
            }

            // Process list type and user context
            if (isset($q['list_type'])) {
                $queryParameters['list_type'] = $q['list_type'];
            }

            // Add user context if needed
            if (auth('api')->user() && auth('api')->user()->getAuthIdentifier() !== 'public-user') {
                $queryParameters['user_context'] = [
                    'username' => auth('api')->user()->getAuthIdentifier(),
                    'sub_party_slug' => auth('api')->user()->sub_party_slug ?? null
                ];
            }

            // Add validation flag
            if (isset($q['is_valid_for_public'])) {
                $queryParameters['is_valid_for_public'] = $q['is_valid_for_public'];
            }

            \Log::info('Contract Progress Request Parameters:', $queryParameters);

            $response = Http::post(config('custom.CUSTOM_API_INTERNAL_ACPMS_BASE_URL') . 'api/internal/tender-contract-data',
                [
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                ],
                json_encode($queryParameters)
            );

            if ($response->status_code > 300) {
                throw new \Exception($response->body);
            }

            $response = json_decode($response->body, true);

            // Return response with pagination headers if available
            $headers = [];
            if (isset($response['pagination']['total'])) {
                $headers['x-pagination-size'] = $response['pagination']['total'];
            }

            return response()->json($response, 200, $headers);

        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }

    public function details(Request $request, $contractNumber)
    {
        try {
            $httpRequest = Http::get(
                config('custom.CUSTOM_API_INTERNAL_ACPMS_BASE_URL') . 'api/internal/contract/' . urlencode($contractNumber) . '/details');

            if ($httpRequest->status_code > 300) {
                throw new \Exception($httpRequest->body);
            }

            $contract_details = json_decode($httpRequest->body, true);
            \Log::info($contract_details);
            return response()->json($contract_details, 200);

        } catch (\Throwable $t) {
            return Error::composeResponse($t);
        }
    }
}

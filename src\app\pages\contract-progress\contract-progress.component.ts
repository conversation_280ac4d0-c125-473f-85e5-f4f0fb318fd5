import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {AuthService} from '../../services/auth.service';
import {NotificationsService} from '../shared/services/notifications.service';
import {ItemService} from '../../services/item.service';
import {ActivatedRoute, Router} from '@angular/router';
import {TranslateService} from '../shared/services/translate.service';
import {MatPaginator, MatPaginatorIntl} from '@angular/material/paginator';
import {formatPaginator} from '../shared/classes/npa-table';
import {PAGINATOR_DEFAULT_PAGE_SIZE, PAGINATOR_PAGE_SIZES} from '../../../environments/environment.const';
import {FormBuilder, FormControl, FormGroup} from '@angular/forms';
import {forkJoin, Observable} from 'rxjs';
import {map, startWith} from 'rxjs/operators';
import {IDropdownData} from '../shared/types/general.types';
import {MatAutocompleteSelectedEvent} from '@angular/material/autocomplete';
import {CdkDragDrop, moveItemInArray} from '@angular/cdk/drag-drop';
import {HttpErrorResponse} from '@angular/common/http';
import {DropdownService} from '../../services/dropdown.service';
import {FormService} from '../shared/services/form.service';
import {Location} from '@angular/common';
import {MatSlideToggleChange} from '@angular/material';
import {ContextAccessService} from '../../services/context-access.service';
import {IContextMenuOption} from '../shared/components/context-menu/context-menu.types';
import {composeMessage} from '../shared/consts/errors';
import {RouteService} from '../../services/route.service';
import {ContractProgressService} from './contract-progress.service';
import {environment as localEnvironment} from '../../../environments/environment';
import {environment as productionEnvironment} from '../../../environments/environment.prod';

@Component({
    selector: 'app-contract-progress',
    templateUrl: './contract-progress.component.html',
    styleUrls: ['./contract-progress.component.styl']
})
export class ContractProgressComponent implements OnInit {
    // ViewChild references
    @ViewChild(MatPaginator, {static: false}) paginator: MatPaginator;
    @ViewChild('entityInput', {static: false}) entityInput: ElementRef<HTMLInputElement>;
    @ViewChild('provinceInput', {static: false}) provinceInput: ElementRef<HTMLInputElement>;

    // Data properties
    processes: any[] = undefined;
    form: FormGroup;
    formData;
    yearObject: { current_year: number; year_list: number[] };
    users: any[] = [];
    portfolios = [];

    // Dropdown data
    procurementTypes: IDropdownData[] = [];
    procurementStatuses: IDropdownData[] = [];
    donors: IDropdownData[] = [];
    provinces: IDropdownData[];
    selectedProvinceItems: IDropdownData[] = [];
    provinceControl: FormControl = new FormControl();
    filteredProvinceList: Observable<IDropdownData[]>;

    procurementEntities: IDropdownData[];
    selectedProcurementEntityItems: IDropdownData[] = [];
    procurementEntityControl: FormControl = new FormControl();
    filteredProcurementEntityList: Observable<IDropdownData[]>;

    // Additional dropdown properties
    contractTypes: IDropdownData[] = [];
    contractStatuses: IDropdownData[] = [];
    fiscalYears: number[] = [];
    procurementMethods: IDropdownData[] = [];
    projectPlanStatuses: IDropdownData[] = [];

    // Contract progress specific dropdowns
    flowStatuses: IDropdownData[] = [];

    // Sorting configuration
    sorting = [
        {
            name_prs: 'شماره قرارداد',
            name_ps: 'شماره قرارداد',
            name_en: 'Contract Number',
            slug: 'contract_number'
        },
        {
            name_prs: 'عنوان قرارداد',
            name_ps: 'عنوان قرارداد',
            name_en: 'Contract Title',
            slug: 'contract_title'
        },
        {
            name_prs: 'نهاد تدارکات',
            name_ps: 'نهاد تدارکات',
            name_en: 'Procurement Entity',
            slug: 'procurement_entity_slug'
        },
        {
            name_prs: 'وضعیت قرارداد',
            name_ps: 'وضعیت قرارداد',
            name_en: 'Contract Status',
            slug: 'contract_status'
        },
        {
            name_prs: 'پیشرفت فزیکی',
            name_ps: 'پیشرفت فزیکی',
            name_en: 'Physical Progress',
            slug: 'physical_progress'
        },
        {
            name_prs: 'کود بودجه',
            name_ps: 'کود بودجه',
            name_en: 'Budget Code',
            slug: 'budget_code_slug'
        }
    ];

    // Status configurations
    projectStatus = [
        {
            name_prs: 'پلان شده',
            name_ps: 'پلان شوی',
            name_en: 'Planned',
            slug: 'planned',
        },
        {
            name_prs: 'فسخ شده',
            name_ps: 'فسخ شوی',
            name_en: 'Cancelled',
            slug: 'cancelled',
        },
    ];

    processingBodies = [
        {
            id: 1,
            name_prs: 'ریاست تدارکات ملی',
            name_ps: 'د ملي تدارکاتو ریاست',
            name_en: 'National procurement authority',
            slug: 'npa'
        },
        {
            id: 2,
            name_prs: 'اداره تدارکاتی',
            name_ps: 'تدارکاتي اداره',
            name_en: 'Procurement entity',
            slug: 'pe'
        },
    ];

    // Context menu
    contextMenuOptionHeader = [];
    contextMenuOptions: IContextMenuOption[] = [];

    // State flags
    checked = false;
    selectable = true;
    removable = true;
    isReset = false;
    isSearched = false;
    isFiltered = false;
    isSelfList = undefined;
    isExceptional = false;
    toggleSearchInMobileMode = false;
    isValidUser = false;

    // User and environment
    user: string = undefined;
    entity: string = undefined;
    clickedIndex: number = 2;
    user_type: string = 'public_list';
    lang = 'en' as string;
    env = localEnvironment || productionEnvironment;
    params;

    constructor(public authService: AuthService,
                public notificationsService: NotificationsService,
                public itemService: ItemService,
                private formBuilder: FormBuilder,
                private router: Router,
                private routeService: RouteService,
                private matPaginatorIntl: MatPaginatorIntl,
                public translateService: TranslateService,
                public dropdownService: DropdownService,
                private contractProgressService: ContractProgressService,
                private location: Location,
                private activatedRoute: ActivatedRoute,
                private formService: FormService,
                public contextAccessService: ContextAccessService) {
    }

    ngOnInit() {
        this.setDropDowns();
        this.initializeComponent();
        this.setupPagination();
        this.setupRouteParams();
        this.setupAuthSubscription();
    }

    private initializeComponent() {
        this.lang = this.translateService.lang === 'prs' ? 'da' : (this.translateService.lang === 'ps' ? 'pa' : 'en');
        this.routeService.toggleSearchInMobileMode.subscribe(value => {
            this.toggleSearchInMobileMode = value;
        });
        this.yearObject = this.contractProgressService.getFiscalYear();
        this.contextMenuOptions = [];
        this.initForm();
    }

    private setupPagination() {
        setTimeout(() => {
            formatPaginator(this.matPaginatorIntl);
            this.paginator.pageSize = PAGINATOR_DEFAULT_PAGE_SIZE;
            this.paginator.pageSizeOptions = PAGINATOR_PAGE_SIZES;
            this.paginator.page.subscribe(() => {
                this.list(this.paginator.pageSize, this.paginator.pageIndex);
            });
        }, 0);
    }

    private setupRouteParams() {
        setTimeout(() => {
            this.activatedRoute.queryParams.subscribe((queryParam) => {
                if (Object.keys(queryParam).length > 0) {
                    this.isSelfList = queryParam.list_type === 'private' && this.authService.loggedInUser !== undefined;
                }
                this.list(
                    queryParam.page_size ? queryParam.page_size : PAGINATOR_DEFAULT_PAGE_SIZE,
                    queryParam.page_index ? queryParam.page_index : 0
                );
                this.paginator.pageSize = queryParam.page_size ? queryParam.page_size : PAGINATOR_DEFAULT_PAGE_SIZE;
                this.paginator.pageIndex = queryParam.page_size ? queryParam.page_index : 0;
            });
        });
    }

    private setupAuthSubscription() {
        this.authService.isLoggedIn.subscribe(value => {
            if (value && this.authService.singleUseToken.value) {
                this.list(PAGINATOR_DEFAULT_PAGE_SIZE, 0);
            }
        });
    }

    initForm() {
        this.form = this.formBuilder.group({
            province_slug: undefined,
            procurement_entity_slug: undefined,
            procurement_type_slug: undefined,
            bidding_search: undefined,
            general_search: undefined,
            flow_status_slug: undefined,
            sort_criteria: undefined,
            procurement_method: undefined,
            // fiscal_year: undefined, // Commented out for now
            project_user: undefined,
            project_plan_status: undefined,
            procurement_processing_body_slug: undefined,
            contract_status: undefined,
            list_type: 'public'
        });
    }

    list(pageSize: number, pageIndex: number, isSearched: boolean = false) {
        this.notificationsService.startLoading();

        if (isSearched) {
            this.paginator.pageIndex = 0;
            this.paginator.pageSize = PAGINATOR_DEFAULT_PAGE_SIZE;
        }

        const pageIndexWhenSearch = isSearched ? 0 : pageIndex;
        this.isSearched = isSearched;

        if (!isSearched) {
            this.setSearchParamsToForm();
        }

        // Fiscal year handling commented out for now
        // if (!this.form.get('fiscal_year').value) {
        //     this.form.get('fiscal_year').setValue([this.yearObject.current_year]);
        // }

        // Update form data
        this.formData = this.form.value;
        this.formData.list_type = this.isSelfList ? 'private' : 'public';
        this.formData.page_size = pageSize;
        this.formData.page_index = pageIndexWhenSearch;

        console.log('Form data before API call:', this.formData);
        console.log('Selected provinces:', this.selectedProvinceItems);
        console.log('Selected entities:', this.selectedProcurementEntityItems);

        this._prepareUrl(this.formService.generateQueryStringObject(this.formData || {}));
        this.callContractsAPI(pageSize, pageIndexWhenSearch, this.formData);
    }

    private prepareSearchParams(formData: any, pageSize: number, pageIndex: number) {
        const searchParams: any = {
            page_size: pageSize || PAGINATOR_DEFAULT_PAGE_SIZE,
            page_index: pageIndex || 0,
            list_type: formData.list_type || 'public'
        };

        // Add search term
        if (formData.bidding_search) {
            searchParams.bidding_search = formData.bidding_search;
        }

        // Add general search term
        if (formData.general_search) {
            searchParams.general_search = formData.general_search;
        }

        // Add province filter with structured data
        if (this.selectedProvinceItems && this.selectedProvinceItems.length > 0) {
            searchParams.province_slug = this.selectedProvinceItems;
        }

        // Add procurement entity filter with structured data
        if (this.selectedProcurementEntityItems && this.selectedProcurementEntityItems.length > 0) {
            searchParams.procurement_entity_slug = this.selectedProcurementEntityItems;
        }

        // Add procurement type filter
        if (formData.procurement_type_slug && formData.procurement_type_slug.length > 0) {
            searchParams.procurement_type_slug = formData.procurement_type_slug;
        }

        // Add flow status filter
        if (formData.flow_status_slug && formData.flow_status_slug.length > 0) {
            searchParams.flow_status_slug = formData.flow_status_slug;
        }

        // Add procurement method filter
        if (formData.procurement_method && formData.procurement_method.length > 0) {
            searchParams.procurement_method = formData.procurement_method;
        }

        // Fiscal year filter commented out for now
        // if (formData.fiscal_year && formData.fiscal_year.length > 0) {
        //     searchParams.fiscal_year = formData.fiscal_year;
        // }

        // Add sort criteria
        if (formData.sort_criteria) {
            searchParams.sort_criteria = formData.sort_criteria;
        }

        // Add procurement processing body
        if (formData.procurement_processing_body_slug) {
            searchParams.procurement_processing_body_slug = formData.procurement_processing_body_slug;
        }

        // Add contract status
        if (formData.contract_status && formData.contract_status.length > 0) {
            searchParams.contract_status = formData.contract_status;
        }

        // Add user context for authenticated users
        if (this.authService.loggedInUser) {
            searchParams.user_context = {
                username: this.authService.loggedInUser.username,
                sub_party_slug: this.authService.loggedInUser.sub_party_slug
            };
        }

        // Add validation flag for public access
        searchParams.is_valid_for_public = !this.authService.loggedInUser || formData.list_type === 'public';

        console.log('Prepared search params:', searchParams);
        return searchParams;
    }

    private callContractsAPI(pageSize: number, pageIndex: number, formData: any) {
        // Prepare complete search parameters
        const apiParams = this.prepareSearchParams(formData, pageSize, pageIndex);

        this.contractProgressService.getContractsList(pageSize, pageIndex, apiParams).subscribe(
            (response) => {
                this.handleContractsResponse(response);
            },
            (error) => {
                this.handleContractsError(error);
            }
        );
    }

    private handleContractsResponse(response: any) {
        console.log('API Response:', response);

        if (response && response.body) {
            // Handle wrapped response
            const responseBody = response.body;
            this.processes = responseBody.contracts || responseBody.tenders || [];

                        // Check for pagination info
            if (responseBody.pagination) {
                this.paginator.length = responseBody.pagination.total || 0;
            } else {
                const headerCount = response.headers && response.headers.get ?
                    parseInt(response.headers.get('x-pagination-size'), 10) : 0;
                this.paginator.length = response.count || headerCount || 0;
            }
        } else if (response && (response.contracts || response.tenders)) {
            // Handle direct response structure
            this.processes = response.contracts || response.tenders || [];

            if (response.pagination) {
                this.paginator.length = response.pagination.total || 0;
            } else {
                this.paginator.length = response.count || this.processes.length;
            }
        } else {
            // Fallback for other response structures
            this.processes = Array.isArray(response) ? response : [];
            this.paginator.length = this.processes.length;
        }

        console.log('Loaded contracts:', this.processes);
        console.log('Total count:', this.paginator.length);

        this.toggleSearchInMobileMode = false;
        this.notificationsService.dismissLoading();
    }

    private handleContractsError(error: any) {
        console.error('Error loading contracts:', error);
        this.processes = [];
        this.paginator.length = 0;
        this.notificationsService.dismissLoading();
        this.notificationsService.error('خطا در بارگیری لست قراردادها');
        console.log(this.processes);
    }

    setDropDowns() {
        forkJoin({
            tenderFlowStatus: this.dropdownService.getData('references', 'tender-flow-status'),
            province: this.dropdownService.getData('commons', 'province'),
            donorSource: this.dropdownService.getData('commons', 'donor-source'),
            procurementEntity: this.dropdownService.getData('commons', 'procurement-entity'),
            procurementType: this.dropdownService.getData('commons', 'procurement-type'),
            procurementMethod: this.dropdownService.getData('commons', 'procurement-method'),
            // contractStatus: this.dropdownService.getData('commons', 'contract-status'),
        }).subscribe(
            (dropDownData: { [dropDownName: string]: IDropdownData[] }) => {
                this.initializeDropdownData(dropDownData);
                this.initializeAutocompleteFilters();
            },
            (error) => {
                this.notificationsService.error(composeMessage(error));
                console.error(error);
            }
        );
    }

    private initializeDropdownData(dropDownData: { [dropDownName: string]: IDropdownData[] }) {
        this.procurementEntities = dropDownData.procurementEntity.sort(
            (a, b) => b.name_prs.localeCompare(a.name_prs)
        );
        this.procurementTypes = dropDownData.procurementType;
        this.provinces = dropDownData.province;
        console.log(this.provinces)
        this.donors = dropDownData.donorSource;
        this.procurementMethods = dropDownData.procurementMethod;
        this.contractStatuses = dropDownData.contractStatus || [];

        const maskedCriteriaVendorPublic = ['initiated', 'planned'];
        if (!this.authService.loggedInUser ||
            (this.authService.loggedInUser && this.authService.loggedInUser.party_slug === 'vendor')) {
            this.procurementStatuses = dropDownData.tenderFlowStatus
                .filter((status) => maskedCriteriaVendorPublic.indexOf(status.slug) === -1);
            this.flowStatuses = dropDownData.tenderFlowStatus
                .filter((status) => maskedCriteriaVendorPublic.indexOf(status.slug) === -1);
        } else {
            this.procurementStatuses = dropDownData.tenderFlowStatus;
            this.flowStatuses = dropDownData.tenderFlowStatus;
        }
    }

    private initializeAutocompleteFilters() {
        this.filteredProvinceList = this.provinceControl.valueChanges.pipe(
            startWith(''),
            map((val: string | null) => val ? this._filterProvince(val) : this.provinces.slice())
        );

        this.filteredProcurementEntityList = this.procurementEntityControl.valueChanges.pipe(
            startWith(''),
            map((val: string | null) => val ? this._filterProcurementEntity(val) : this.procurementEntities.slice())
        );
    }

    refresh() {
        this.list(this.paginator.pageSize, this.paginator.pageIndex, false);
    }

    loadSelfTenders(event: MatSlideToggleChange) {
        this.list(this.paginator.pageSize, this.paginator.pageIndex, false);
    }

    isValid(): boolean {
        return undefined;
    }

    selectProcurementProcess(process: any) {
        console.log('Selected contract:', process);
        if (process && process.contract_number) {
            this.router.navigate([`/${this.translateService.lang}/contract-progress`, process.contract_number, 'details']);
        } else {
            console.error('Contract number not found in process:', process);
            this.notificationsService.error('شماره قرارداد یافت نشد');
        }
    }

    submit(pageSize, pageIndex, isSearched) {
        this.isFiltered = true;
        this.list(pageSize, pageIndex, isSearched);
    }

    reset() {
        this.isReset = true;
        this.selectedProcurementEntityItems = [];
        this.selectedProvinceItems = [];
        this.form.reset();

        this.clearInputFields();
        this.resetAutocompleteControls();
        this.resetPagination();

        this.list(PAGINATOR_DEFAULT_PAGE_SIZE, 0, true);
        this.router.navigateByUrl(`${this.translateService.lang}/contract-progress`);
    }

    private clearInputFields() {
        if (this.entityInput && this.entityInput.nativeElement) {
            this.entityInput.nativeElement.value = '';
        }
        if (this.provinceInput && this.provinceInput.nativeElement) {
            this.provinceInput.nativeElement.value = '';
        }
    }

    private resetAutocompleteControls() {
        this.procurementEntityControl.setValue(null);
        this.provinceControl.setValue(null);
    }

    private resetPagination() {
        if (this.paginator) {
            this.paginator.pageIndex = 0;
            this.paginator.pageSize = PAGINATOR_DEFAULT_PAGE_SIZE;
        }
    }

    removeProvince(slug: string): void {
        const index = this.selectedProvinceItems.findIndex(item => item.slug === slug);
        if (index >= 0) {
            this.selectedProvinceItems.splice(index, 1);
        }
    }

    removeProcurementEntity(slug: string): void {
        const index = this.selectedProcurementEntityItems.findIndex(item => item.slug === slug);
        if (index >= 0) {
            this.selectedProcurementEntityItems.splice(index, 1);
        }
    }

    selectedProvince(event: MatAutocompleteSelectedEvent): void {
        const index = this.selectedProvinceItems.findIndex(item => item.slug === event.option.value.slug);
        if (index >= 0) {
            this.selectedProvinceItems.splice(index, 1);
        }
        this.selectedProvinceItems.push(event.option.value);
        this.provinceInput.nativeElement.value = '';
        this.provinceControl.setValue(null);
        this.form.get('province_slug').setValue(this.selectedProvinceItems);
    }

    selectedProcurementEntity(event: MatAutocompleteSelectedEvent): void {
        const index = this.selectedProcurementEntityItems.findIndex(item => item.slug === event.option.value.slug);
        if (index >= 0) {
            this.selectedProcurementEntityItems.splice(index, 1);
        }
        this.selectedProcurementEntityItems.push(event.option.value);
        this.entityInput.nativeElement.value = '';
        this.procurementEntityControl.setValue(null);
        this.form.get('procurement_entity_slug').setValue(this.selectedProcurementEntityItems);
    }

    private _filterProvince(value: string): IDropdownData[] {
        return this.provinces.filter(item => item.name_prs.includes(value));
    }

    private _filterProcurementEntity(value: string): IDropdownData[] {
        return this.procurementEntities.filter(item => item.name_prs.includes(value));
    }

    drop(event: CdkDragDrop<string[]>) {
        moveItemInArray(this.sorting, event.previousIndex, event.currentIndex);
    }

    setSearchParamsToForm() {
        this.formData = this.form.value;
        this.activatedRoute.queryParams.subscribe((urlParams) => {
            if (Object.keys(urlParams).length > 0) {
                const params = Object.assign({}, urlParams);
                for (const param in params) {
                    if (params.hasOwnProperty(param)) {
                        if (param === 'page_size' || param === 'page_index' || param === 'list_type') {
                            this.formData[param] = params[param];
                        } else if (param === 'procurement_entity_slug') {
                            const entitiesSlug = params[param].split(',');

                            for (const slug of entitiesSlug) {
                                const procurementEntity = this.dropdownService
                                    .getItem('commons', 'procurement-entity', slug);
                                this.selectedProcurementEntityItems.push(procurementEntity);
                            }
                            this.form.get(param).setValue(this.selectedProcurementEntityItems);
                        } else if (param === 'province_slug') {
                            const provincesSlug = params[param].split(',');

                            for (const slug of provincesSlug) {
                                const province = this.dropdownService
                                    .getItem('commons', 'province', slug);
                                this.selectedProvinceItems.push(province);
                            }
                            this.form.get(param).setValue(this.selectedProvinceItems);
                        } else if (param === 'procurement_type_slug') {
                            const procurementTypesSlug = params[param].split(',');
                            this.form.get(param).setValue(procurementTypesSlug);
                        } else if (param === 'flow_status_slug') {
                            const flowStatusSlug = params[param].split(',');
                            this.form.get(param).setValue(flowStatusSlug);
                        } else if (param === 'portfolio') {
                            const portfolioSlug = params[param].split(',');
                            this.form.get(param).setValue(portfolioSlug);
                        } else if (param === 'contract_status') {
                            const contractStatusSlug = params[param].split(',');
                            this.form.get(param).setValue(contractStatusSlug);
                        } else if (param === 'procurement_method') {
                            const procurementMethodSlug = params[param].split(',');
                            this.form.get(param).setValue(procurementMethodSlug);
                        } else {
                            if (this.form.contains(param)) {
                                this.form.get(param).setValue(params[param]);
                            }
                        }
                    }
                }
            }
        });
    }

    private _prepareUrl(searchParams) {
        let path = this.router.url;
        const temp = path.split('?');
        if (Object.keys(searchParams).length <= 2 && this.isReset) {
            path = this.isReset ? temp[0] : path;
            this.location.replaceState(path);
            return;
        }
        this.isSearched = true;
        path = temp[0] + '?';
        for (const key in searchParams) {
            if (searchParams[key] !== null || searchParams[key] !== '') {
                const tempSlug = [];
                if (key === 'procurement_entity_slug' || key === 'province_slug') {
                    for (const param of searchParams[key]) {
                        tempSlug.push(param.slug);
                    }
                    if (searchParams[key] && searchParams[key].length !== 0) {
                        path += key + '=' + tempSlug + '&';
                    }
                } else if (key === 'procurement_type_slug' || key === 'portfolio' || key === 'flow_status_slug' || key === 'contract_status' || key === 'procurement_method') {
                    if (searchParams[key] && searchParams[key].length !== 0) {
                        path += key + '=' + searchParams[key] + '&';
                    }
                } else {
                    path += key + '=' + searchParams[key] + '&';
                }
            }
        }
        this.location.replaceState(path);
    }

    getPercentageColor(percentageString: string): string {
        if (!percentageString) {
            return '#000000';
        }

        const percentage = parseFloat(percentageString);
        if (percentage === 0.0) {
            return '#FF3300';
        } else if (percentage >= 1.0 && percentage <= 39.9) {
            return '#EE841A';
        } else if (percentage >= 40.0 && percentage <= 100.0) {
            return '#0acf38';
        }
        return '#000000';
    }


}
